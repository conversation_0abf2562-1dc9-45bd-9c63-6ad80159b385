# POS - Point of Sale Application

Aplicación de punto de venta desarrollada con Spring Boot 3.5.5 y Java 21.

## 🚀 Ejecución con Docker

### Prerrequisitos
- Docker
- Docker Compose

### 📦 Construir la imagen

```bash
docker build -t pos-app:latest .
```

### 🐳 Ejecutar la aplicación

#### Opción 1: Con Docker Compose (Recomendado)
```bash
# Iniciar la aplicación
docker-compose up -d

# Iniciar con logs visibles
docker-compose up
```

#### Opción 2: Con Docker directo
```bash
docker run -p 8080:8080 --name pos-container pos-app:latest
```

### 📊 Monitoreo y Logs

```bash
# Ver logs en tiempo real
docker-compose logs -f pos-app

# Ver logs de las últimas 100 líneas
docker-compose logs --tail=100 pos-app

# Ver estado de los contenedores
docker-compose ps
```

### 🛑 Detener la aplicación

```bash
# Detener contenedores
docker-compose down

# Detener y eliminar volúmen<PERSON>
docker-compose down -v

# Detener contenedor individual
docker stop pos-container
```

### 🔧 Comandos útiles

```bash
# Reconstruir la imagen sin caché
docker build --no-cache -t pos-app:latest .

# Ejecutar comandos dentro del contenedor
docker-compose exec pos-app bash

# Ver información de la imagen
docker inspect pos-app:latest

# Limpiar imágenes no utilizadas
docker image prune

# Ver uso de recursos
docker stats
```

### 🏥 Health Checks

La aplicación incluye endpoints de monitoreo:

- **Aplicación**: http://localhost:8080
- **Health Check**: http://localhost:8080/actuator/health
- **Info**: http://localhost:8080/actuator/info

### 🗄️ Base de datos (Opcional)

Para habilitar PostgreSQL, descomenta la sección de base de datos en `docker-compose.yml` y configura las propiedades en `application-docker.properties`.

```bash
# Iniciar con base de datos
docker-compose up -d postgres pos-app
```

### 🔄 Desarrollo

```bash
# Reconstruir y reiniciar después de cambios
docker-compose down
docker build -t pos-app:latest .
docker-compose up -d

# Ver logs durante desarrollo
docker-compose logs -f pos-app
```

### 🧹 Limpieza completa

```bash
# Eliminar todo (contenedores, imágenes, volúmenes)
docker-compose down -v
docker rmi pos-app:latest
docker system prune -f
```

## 📁 Estructura del proyecto

```
pos/
├── src/
│   └── main/
│       ├── java/
│       └── resources/
│           ├── application.properties
│           └── application-docker.properties
├── Dockerfile
├── docker-compose.yml
├── .dockerignore
├── Makefile
└── README.md
```

## ⚙️ Configuración

- **Puerto**: 8080
- **Perfil Docker**: `docker`
- **Java**: 21
- **Spring Boot**: 3.5.5

## 🛠️ Makefile (Alternativa)

Si prefieres usar Makefile:

```bash
make help      # Ver comandos disponibles
make build     # Construir imagen
make run       # Ejecutar aplicación
make logs      # Ver logs
make stop      # Detener aplicación
make clean     # Limpiar todo
```

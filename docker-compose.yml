version: '3.8'

services:
  pos-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: pos-app
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
    networks:
      - pos-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Uncomment and configure if you need a database
  # postgres:
  #   image: postgres:15-alpine
  #   container_name: pos-postgres
  #   environment:
  #     POSTGRES_DB: pos_db
  #     POSTGRES_USER: pos_user
  #     POSTGRES_PASSWORD: pos_password
  #   ports:
  #     - "5432:5432"
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #   networks:
  #     - pos-network
  #   restart: unless-stopped

networks:
  pos-network:
    driver: bridge

# volumes:
#   postgres_data:

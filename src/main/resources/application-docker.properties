# Docker-specific configuration
spring.application.name=pos

# Server configuration
server.port=8080

# Actuator configuration for health checks
management.endpoints.web.exposure.include=health,info
management.endpoint.health.show-details=when-authorized
management.health.defaults.enabled=true

# Logging configuration
logging.level.com.pe.innovatech=INFO
logging.level.org.springframework=WARN
logging.level.org.hibernate=WARN

# Database configuration (uncomment and configure as needed)
# spring.datasource.url=**************************************
# spring.datasource.username=pos_user
# spring.datasource.password=pos_password
# spring.datasource.driver-class-name=org.postgresql.Driver
# spring.jpa.hibernate.ddl-auto=update
# spring.jpa.show-sql=false
# spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect

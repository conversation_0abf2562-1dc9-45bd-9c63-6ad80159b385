#!/bin/bash

# Script para construir y ejecutar la aplicación POS con Docker

echo "🚀 Construyendo la aplicación POS..."

# Construir la imagen Docker
docker build -t pos-app:latest .

if [ $? -eq 0 ]; then
    echo "✅ Imagen construida exitosamente"
    
    echo "🐳 Iniciando la aplicación con Docker Compose..."
    docker-compose up -d
    
    if [ $? -eq 0 ]; then
        echo "✅ Aplicación iniciada exitosamente"
        echo "📱 La aplicación está disponible en: http://localhost:8080"
        echo "🏥 Health check disponible en: http://localhost:8080/actuator/health"
        echo ""
        echo "Para ver los logs: docker-compose logs -f pos-app"
        echo "Para detener: docker-compose down"
    else
        echo "❌ Error al iniciar la aplicación"
        exit 1
    fi
else
    echo "❌ Error al construir la imagen"
    exit 1
fi

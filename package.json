{"name": "pos-docker-scripts", "version": "1.0.0", "description": "Scripts para dockerizar la aplicación POS", "scripts": {"docker:build": "docker build -t pos-app:latest .", "docker:run": "docker-compose up -d", "docker:stop": "docker-compose down", "docker:logs": "docker-compose logs -f pos-app", "docker:dev": "npm run docker:build && npm run docker:run", "docker:clean": "docker-compose down -v && docker rmi pos-app:latest || true"}}
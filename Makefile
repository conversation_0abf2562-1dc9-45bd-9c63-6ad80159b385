.PHONY: build run stop logs clean help

# Variables
IMAGE_NAME = pos-app
TAG = latest

help: ## Mostrar ayuda
	@echo "Comandos disponibles:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-15s\033[0m %s\n", $$1, $$2}'

build: ## Construir la imagen Docker
	docker build -t $(IMAGE_NAME):$(TAG) .

run: ## Ejecutar la aplicación con docker-compose
	docker-compose up -d

stop: ## Detener la aplicación
	docker-compose down

logs: ## Ver logs de la aplicación
	docker-compose logs -f pos-app

clean: ## Limpiar imágenes y contenedores
	docker-compose down -v
	docker rmi $(IMAGE_NAME):$(TAG) || true

dev: build run ## Construir y ejecutar (desarrollo)

restart: stop run ## Reiniciar la aplicación
